# 前后端联调设计文档

## 概述

本设计文档描述了BoneVue前端（Vue 3 + TypeScript + TDesign）和BoneSys后端（Spring Boot + MySQL）系统的集成架构。系统采用RESTful API架构，通过Vite代理实现开发环境的前后端通信，使用JWT进行用户认证，并建立统一的数据传输格式。

## 架构

### 系统架构图

```mermaid
graph TB
    subgraph "前端层 (Bone_Vue)"
        A[Vue 3 应用] --> B[Vite 开发服务器]
        B --> C[API 代理层]
        A --> D[TDesign UI组件]
        A --> E[Pinia 状态管理]
        A --> F[Vue Router 路由]
        A --> G[Axios HTTP客户端]
    end
    
    subgraph "网络层"
        C --> H[HTTP/HTTPS 通信]
    end
    
    subgraph "后端层 (BoneSys)"
        H --> I[Spring Boot 应用]
        I --> J[CORS 配置]
        I --> K[JWT 认证]
        I --> L[REST 控制器]
        L --> M[业务服务层]
        M --> N[数据访问层]
        N --> O[MySQL 数据库]
        M --> P[硬件通信层]
        P --> Q[串口设备]
    end
```

### 通信流程

```mermaid
sequenceDiagram
    participant F as 前端 (Vue)
    participant P as Vite代理
    participant B as 后端 (Spring Boot)
    participant D as 数据库
    participant H as 硬件设备
    
    F->>P: HTTP请求 (/api/*)
    P->>B: 代理转发 (localhost:8080)
    B->>B: CORS验证
    B->>B: JWT认证验证
    B->>D: 数据库操作
    B->>H: 硬件控制 (可选)
    B->>P: ApiResponse格式响应
    P->>F: 响应数据
    F->>F: 数据处理和UI更新
```

## 组件和接口

### 前端组件架构

#### 1. HTTP客户端配置
- **文件**: `src/utils/axios.ts`
- **功能**: 
  - 配置基础URL为 `/api`
  - 请求/响应拦截器
  - 错误处理和用户提示
  - JWT令牌自动添加

#### 2. API服务层
- **结构**: `src/api/` 目录
- **功能**: 
  - 封装所有后端API调用
  - 类型安全的请求/响应接口
  - 统一的错误处理

#### 3. 状态管理
- **工具**: Pinia
- **功能**:
  - 用户认证状态
  - 患者数据缓存
  - 治疗进程状态
  - 硬件设备状态

#### 4. 路由守卫
- **功能**:
  - 认证状态检查
  - 权限验证
  - 自动重定向

### 后端接口设计

#### 1. 统一响应格式
```typescript
interface ApiResponse<T> {
  code: number;        // 响应码
  message: string;     // 响应消息
  data: T | null;      // 响应数据
  timestamp: string;   // 时间戳
}
```

#### 2. 核心API端点

**认证相关**
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/verify` - 令牌验证

**患者管理**
- `GET /api/patients` - 获取患者列表
- `POST /api/patients` - 创建患者
- `PUT /api/patients/{id}` - 更新患者信息
- `DELETE /api/patients/{id}` - 删除患者
- `GET /api/patients/{id}` - 获取患者详情

**治疗进程**
- `GET /api/processes` - 获取治疗进程列表
- `POST /api/processes` - 创建治疗进程
- `PUT /api/processes/{id}` - 更新治疗进程
- `DELETE /api/processes/{id}` - 删除治疗进程
- `POST /api/processes/{id}/start` - 启动治疗
- `POST /api/processes/{id}/stop` - 停止治疗

**硬件控制**
- `GET /api/hardware/treatment-heads` - 获取治疗头状态
- `POST /api/hardware/treatment-heads/{id}/control` - 控制治疗头
- `GET /api/hardware/treatment-heads/sync` - 同步状态

**系统监控**
- `GET /api/health` - 健康检查
- `GET /api/dashboard` - 仪表板数据
- `GET /api/statistics` - 统计数据

#### 3. 错误码规范
- `200`: 操作成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误
- `1001`: 硬件通信异常
- `1002`: 治疗头不可用
- `1003`: 患者信息重复

## 数据模型

### 前端TypeScript接口

```typescript
// 用户认证
interface LoginRequest {
  username: string;
  password: string;
}

interface LoginResponse {
  token: string;
  user: UserInfo;
}

interface UserInfo {
  id: number;
  username: string;
  role: string;
}

// 患者管理
interface Patient {
  id: number;
  name: string;
  age: number;
  gender: string;
  phone: string;
  diagnosis: string;
  createdAt: string;
  updatedAt: string;
}

interface PatientCreateRequest {
  name: string;
  age: number;
  gender: string;
  phone: string;
  diagnosis: string;
}

// 治疗进程
interface TreatmentProcess {
  id: number;
  patientId: number;
  patientName: string;
  status: 'PENDING' | 'RUNNING' | 'COMPLETED' | 'CANCELLED';
  treatmentHeads: number[];
  startTime: string;
  endTime?: string;
  duration: number;
  progress: number;
}

// 硬件设备
interface TreatmentHead {
  id: number;
  name: string;
  status: 'AVAILABLE' | 'BUSY' | 'MAINTENANCE' | 'ERROR';
  temperature: number;
  power: number;
  lastUpdate: string;
}

// 分页数据
interface PaginationInfo {
  page: number;
  size: number;
  total: number;
  totalPages: number;
}

interface PagedResponse<T> {
  content: T[];
  pagination: PaginationInfo;
}
```

### 后端实体映射

后端已有完整的JPA实体类，包括：
- `Patient` - 患者实体
- `TreatmentProcess` - 治疗进程实体
- `TreatmentHead` - 治疗头实体
- `User` - 用户实体

## 错误处理

### 前端错误处理策略

#### 1. HTTP错误处理
```typescript
// axios响应拦截器中的错误处理
axiosInstance.interceptors.response.use(
  response => response,
  error => {
    const { status, data } = error.response || {};
    
    switch (status) {
      case 401:
        // 清除认证状态，跳转登录
        authStore.logout();
        router.push('/login');
        break;
      case 403:
        MessagePlugin.error('权限不足');
        break;
      case 404:
        MessagePlugin.error('资源不存在');
        break;
      case 500:
        MessagePlugin.error('服务器错误');
        break;
      case 1001:
        MessagePlugin.error('硬件通信异常');
        break;
      case 1002:
        MessagePlugin.error('治疗头不可用');
        break;
      case 1003:
        MessagePlugin.error('患者信息重复');
        break;
      default:
        MessagePlugin.error(data?.message || '未知错误');
    }
    
    return Promise.reject(error);
  }
);
```

#### 2. 业务错误处理
- 表单验证错误显示
- 操作失败提示
- 网络超时重试机制
- 离线状态检测

### 后端错误处理

后端已实现全局异常处理器 `GlobalExceptionHandler`，统一处理：
- 参数验证异常
- 业务逻辑异常
- 硬件通信异常
- 数据库操作异常

## 测试策略

### 前端测试

#### 1. 单元测试
- Vue组件测试
- API服务测试
- 工具函数测试
- 状态管理测试

#### 2. 集成测试
- API接口集成测试
- 路由导航测试
- 用户交互流程测试

#### 3. E2E测试
- 完整用户流程测试
- 跨浏览器兼容性测试

### 后端测试

#### 1. API测试
- REST接口功能测试
- 参数验证测试
- 错误响应测试

#### 2. 集成测试
- 数据库集成测试
- 硬件通信测试
- 认证授权测试

### 联调测试

#### 1. 开发环境测试
- 本地代理配置测试
- API接口联通性测试
- 数据传输格式验证

#### 2. 功能测试
- 用户认证流程测试
- 患者管理功能测试
- 治疗进程控制测试
- 硬件设备监控测试

#### 3. 性能测试
- API响应时间测试
- 并发请求处理测试
- 前端渲染性能测试

#### 4. 错误处理测试
- 网络异常处理测试
- 服务器错误处理测试
- 硬件故障处理测试

## 开发环境配置

### 前端开发配置

#### 1. Vite代理配置
```typescript
// vite.config.ts
export default defineConfig({
  server: {
    host: '0.0.0.0',
    proxy: {
      '/api': {
        target: 'http://127.0.0.1:8080',
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
});
```

#### 2. 环境变量配置
```bash
# .env.development
VITE_API_BASE_URL=/api
VITE_APP_TITLE=FREEBONE医疗设备管理系统

# .env.production
VITE_API_BASE_URL=https://api.example.com
VITE_APP_TITLE=FREEBONE医疗设备管理系统
```

### 后端开发配置

#### 1. CORS配置
```properties
# application-dev.properties
cors.allowed-origins=http://localhost:3000,http://127.0.0.1:3000
cors.allowed-methods=GET,POST,PUT,DELETE,OPTIONS
cors.allowed-headers=*
```

#### 2. 数据库配置
```properties
# application-dev.properties
spring.datasource.url=***********************************
spring.datasource.username=root
spring.datasource.password=123456
```

### 部署配置

#### 1. 生产环境前端配置
- 构建优化配置
- 静态资源CDN配置
- 缓存策略配置

#### 2. 生产环境后端配置
- 数据库连接池配置
- 日志级别配置
- 安全策略配置
- 监控配置

## 安全考虑

### 1. 认证安全
- JWT令牌过期机制
- 刷新令牌策略
- 登录状态持久化

### 2. 通信安全
- HTTPS传输加密
- CORS策略配置
- 请求头验证

### 3. 数据安全
- 输入参数验证
- SQL注入防护
- XSS攻击防护

### 4. 硬件安全
- 串口通信加密
- 设备状态验证
- 异常状态处理

## 性能优化

### 1. 前端优化
- 组件懒加载
- 路由懒加载
- 静态资源压缩
- 缓存策略

### 2. 后端优化
- 数据库查询优化
- 连接池配置
- 缓存机制
- 异步处理

### 3. 网络优化
- HTTP/2支持
- 资源压缩
- CDN加速
- 请求合并

## 监控和日志

### 1. 前端监控
- 错误监控
- 性能监控
- 用户行为分析

### 2. 后端监控
- API性能监控
- 数据库性能监控
- 硬件状态监控
- 系统资源监控

### 3. 日志管理
- 结构化日志
- 日志级别管理
- 日志轮转
- 日志分析